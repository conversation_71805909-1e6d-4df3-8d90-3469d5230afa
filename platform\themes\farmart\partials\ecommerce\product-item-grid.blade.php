<div class="product-thumbnail" data-product-id="{{ $product->id }}">
    <a
        class="product-loop__link img-fluid-eq"
        href="{{ $product->url }}"
        tabindex="0"
    >
        <div class="img-fluid-eq__dummy"></div>
        <div class="img-fluid-eq__wrap">
            <img
                class="lazyload product-thumbnail__img"
                data-src="{{ RvMedia::getImageUrl($product->image, 'small', false, RvMedia::getDefaultImage()) }}"
                src="{{ image_placeholder($product->image, 'small') }}"
                alt="{{ $product->name }}"
            >
        </div>
        <span class="ribbons">
            @if ($product->isOutOfStock())
                <span class="ribbon out-stock">{{ __('Out Of Stock') }}</span>
            @else
                @if ($product->productLabels->isNotEmpty())
                    @foreach ($product->productLabels as $label)
                        <span
                            class="ribbon"
                            @if ($label->color) style="background-color: {{ $label->color }}" @endif
                        >{{ $label->name }}</span>
                    @endforeach
                @else
                    @if ($product->front_sale_price !== $product->price)
                        <div
                            class="featured ribbon"
                            dir="ltr"
                        >{{ get_sale_percentage($product->price, $product->front_sale_price) }}</div>
                    @endif
                @endif
            @endif
        </span>
    </a>
</div>
<div class="product-details position-relative">
    <div class="product-content-box" style="padding: 15px;">
        @if (is_plugin_active('marketplace') && $product->store->id)
            <div class="sold-by-meta">
                <a
                    href="{{ $product->store->url }}"
                    tabindex="0"
                    style="color: #000080 !important; border: 1px solid #000080; border-radius: 4px; padding: 2px 8px; display: inline-block; margin-bottom: 5px; text-decoration: none; font-size: 12px; font-weight: 500; transition: all 0.3s ease; background-color: rgba(0, 0, 128, 0.05);"
                    onmouseover="this.style.color='#ff6633'; this.style.borderColor='#ff6633'; this.style.backgroundColor='rgba(255, 102, 51, 0.05)'; this.style.boxShadow='0 2px 4px rgba(0, 0, 0, 0.1)';"
                    onmouseout="this.style.color='#000080'; this.style.borderColor='#000080'; this.style.backgroundColor='rgba(0, 0, 128, 0.05)'; this.style.boxShadow='none';"
                >{{ $product->store->name }}</a>
            </div>
        @endif
        <h3 class="product__title" style="color: #000000 !important;">
            <a
                href="{{ $product->url }}"
                tabindex="0"
                style="color: #000000 !important; font-weight: 500;"
                onmouseover="this.style.color='#ff6633'"
                onmouseout="this.style.color='#000000'"
            >{{ $product->name }}</a>
        </h3>
        @if (EcommerceHelper::isReviewEnabled())
            {!! Theme::partial('star-rating', ['avg' => $product->reviews_avg, 'count' => $product->reviews_count]) !!}
        @endif
        <div style="margin-top: 8px;">
            {!! Theme::partial('ecommerce.product-price', compact('product')) !!}
        </div>
    </div>
</div>