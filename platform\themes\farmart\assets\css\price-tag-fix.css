/* Fix to make green price tags the same size as orange ones */

/* Target all price tags to ensure consistent styling */
.product-price ins,
.product-price .amount,
.product-price-original bdi,
.product-price-sale bdi,
.price-amount,
.price-amount .amount,
.product-price-original .price-amount,
.product-price-sale .price-amount,
.product-inner .product-price,
.product-inner .product-price-original,
.product-inner .product-price-sale,
.product-inner .product-price span,
.product-inner .product-price-original span,
.product-inner .product-price-sale span {
    font-size: 18px !important;
    font-weight: 700 !important;
    line-height: 1.5 !important;
}

/* Specifically target the green price tags */
.product-inner .product-price[style*="color: green"],
.product-inner .product-price[style*="color:green"],
.product-inner .product-price .amount[style*="color: green"],
.product-inner .product-price .amount[style*="color:green"],
.product-inner .product-price-original[style*="color: green"],
.product-inner .product-price-original[style*="color:green"],
.product-inner .product-price-sale[style*="color: green"],
.product-inner .product-price-sale[style*="color:green"],
.product-inner .price-amount[style*="color: green"],
.product-inner .price-amount[style*="color:green"] {
    font-size: 18px !important;
    font-weight: 700 !important;
    color: #ff6633 !important; /* Match the orange color */
}

/* Target the specific green price tags */
.product-price .text-success,
.product-price-original .text-success,
.product-price-sale .text-success,
.price-amount .text-success,
.product-inner span[style*="color: green"],
.product-inner span[style*="color:green"],
.product-inner .text-success {
    font-size: 18px !important;
    font-weight: 700 !important;
    color: #ff6633 !important; /* Match the orange color */
}

/* Fix for the specific green price tags shown in the image */
span[style*="color: green"],
span[style*="color:green"],
.text-success {
    font-size: 18px !important;
    font-weight: 700 !important;
    color: #ff6633 !important; /* Match the orange color */
}

/* Ensure consistent styling for all price amounts */
.amount:not(del .amount):not(del bdi):not(del span),
.product-inner .amount:not(del .amount):not(del bdi):not(del span),
.product-inner .product-price .amount:not(del .amount):not(del bdi):not(del span),
.product-inner .product-price-original .amount:not(del .amount):not(del bdi):not(del span),
.product-inner .product-price-sale .amount:not(del .amount):not(del bdi):not(del span),
.product-inner .price-amount .amount:not(del .amount):not(del bdi):not(del span) {
    font-size: 18px !important;
    font-weight: 700 !important;
    color: #ff6633 !important;
}

/* Desktop Price Tag - Make main price bigger on desktop */
@media (min-width: 993px) {
    /* Main product detail page price */
    .amount:not(del .amount):not(del bdi):not(del span),
    .product-inner .amount:not(del .amount):not(del bdi):not(del span),
    .product-inner .product-price .amount:not(del .amount):not(del bdi):not(del span),
    .product-inner .product-price-original .amount:not(del .amount):not(del bdi):not(del span),
    .product-inner .product-price-sale .amount:not(del .amount):not(del bdi):not(del span),
    .product-inner .price-amount .amount:not(del .amount):not(del bdi):not(del span),
    .bb-product-price-text,
    .bb-product-price .price-amount bdi .amount,
    .product-price-sale ins .price-amount bdi .amount,
    .product-price-original .price-amount bdi .amount {
        font-size: 28px !important;
        font-weight: 700 !important;
        color: #ff6633 !important;
    }

    /* Keep original price smaller on desktop */
    .product-price del .amount,
    .product-price-sale del .amount,
    del .amount,
    .bb-product-price-text-old,
    .product-price-sale del .price-amount bdi .amount {
        font-size: 18px !important;
        text-decoration: line-through !important;
        opacity: 0.8 !important;
    }
}

/* Fix for deleted prices */
.product-price del,
.product-price-sale del,
del,
.product-inner del,
.product-inner .product-price del,
.product-inner .product-price-sale del,
.product-inner .price-amount del {
    color: #999 !important;
    font-weight: normal !important;
    font-size: 14px !important;
    margin-left: 5px !important;
    text-decoration: line-through !important;
}

/* Specifically target the crossed-out original price */
del .amount,
del bdi,
del span,
.product-inner del .amount,
.product-inner del bdi,
.product-inner del span,
.product-price-sale del .amount,
.product-price-sale del bdi,
.product-price-sale del span,
.product-inner .product-price-sale del .amount,
.product-inner .product-price-sale del bdi,
.product-inner .product-price-sale del span,
.product-inner small del,
.product-inner small del .amount,
.product-inner small del bdi,
.product-inner small del span {
    color: #999 !important;
    font-weight: normal !important;
    font-size: 14px !important;
    text-decoration: line-through !important;
}
