<div class="product-thumbnail">
    <a
        class="product-loop__link img-fluid-eq"
        href="{{ $product->url }}"
        tabindex="0"
    >
        <div class="img-fluid-eq__dummy"></div>
        <div class="img-fluid-eq__wrap">
            <img
                class="lazyload product-thumbnail__img"
                data-src="{{ RvMedia::getImageUrl($product->image, 'small', false, RvMedia::getDefaultImage()) }}"
                src="{{ image_placeholder($product->image, 'small') }}"
                alt="{{ $product->name }}"
            >
        </div>
        <span class="ribbons">
            @if ($product->isOutOfStock())
                <span class="ribbon out-stock">{{ __('Out Of Stock') }}</span>
            @else
                @if ($product->productLabels->isNotEmpty())
                    @foreach ($product->productLabels as $label)
                        <span
                            class="ribbon"
                            @if ($label->color) style="background-color: {{ $label->color }}" @endif
                        >{{ $label->name }}</span>
                    @endforeach
                @else
                    @if ($product->front_sale_price !== $product->price)
                        <div
                            class="featured ribbon"
                            dir="ltr"
                        >{{ get_sale_percentage($product->price, $product->front_sale_price) }}</div>
                    @endif
                @endif
            @endif
        </span>
    </a>
    <!-- Action buttons removed -->
</div>
<div class="product-details position-relative">
    <!-- Mobile/Tablet Price Tag (before title) -->
    <div class="mobile-price-tag" style="background-color: #ff6633; margin: -10px -15px 15px -15px; padding: 10px 15px; width: calc(100% + 30px); position: relative; color: #fff; display: none;">
        @if ($product->front_sale_price !== $product->price)
            <div class="price-tag-content" style="display: flex; align-items: center; justify-content: flex-start; height: 40px;">
                <div style="display: flex; flex-direction: column; justify-content: center;">
                    <span class="sale-price" style="font-size: 18px; font-weight: 700; line-height: 1.2; text-align: left;">{{ format_price($product->front_sale_price_with_taxes) }}</span>
                    <span class="regular-price" style="font-size: 14px; text-decoration: line-through; opacity: 0.8; line-height: 1.2; text-align: left;">{{ format_price($product->price_with_taxes) }}</span>
                </div>
            </div>
        @else
            <div class="price-tag-content" style="display: flex; align-items: center; justify-content: flex-start; height: 40px;">
                <span class="regular-price-only" style="font-size: 18px; font-weight: 700;">{{ format_price($product->front_sale_price_with_taxes) }}</span>
            </div>
        @endif
    </div>

    <div class="product-content-box" style="padding-top: 10px;">
        @if (is_plugin_active('marketplace') && $product->store->id)
            <div class="sold-by-meta">
                <a
                    href="{{ $product->store->url }}"
                    tabindex="0"
                    style="color: #000080 !important; border: 1px solid #000080; border-radius: 4px; padding: 2px 8px; display: inline-block; margin-bottom: 5px; text-decoration: none; font-size: 12px; font-weight: 500; transition: all 0.3s ease; background-color: rgba(0, 0, 128, 0.05);"
                    onmouseover="this.style.color='#ff6633'; this.style.borderColor='#ff6633'; this.style.backgroundColor='rgba(255, 102, 51, 0.05)'; this.style.boxShadow='0 2px 4px rgba(0, 0, 0, 0.1)';"
                    onmouseout="this.style.color='#000080'; this.style.borderColor='#000080'; this.style.backgroundColor='rgba(0, 0, 128, 0.05)'; this.style.boxShadow='none';"
                >{{ $product->store->name }}</a>
            </div>
        @endif
        <h3 class="product__title" style="color: #000000 !important;">
            <a
                href="{{ $product->url }}"
                tabindex="0"
                style="color: #000000 !important; font-weight: 500;"
                onmouseover="this.style.color='#ff6633'"
                onmouseout="this.style.color='#000000'"
            >{{ $product->name }}</a>
        </h3>
        @if (EcommerceHelper::isReviewEnabled())
            {!! Theme::partial('star-rating', ['avg' => $product->reviews_avg, 'count' => $product->reviews_count]) !!}
        @endif
        <div class="d-none d-md-block" style="margin-top: 8px;">
            {!! Theme::partial('ecommerce.product-price', compact('product')) !!}
        </div>
        @if (!empty($isFlashSale))
            <div class="deal-sold row mt-2">
                @if (Botble\Ecommerce\Facades\FlashSale::isShowSaleCountLeft())
                    <div class="deal-text col-auto">
                        <span class="sold fw-bold">
                            @if ($product->pivot->quantity > $product->pivot->sold)
                                <span class="text">{{ __('Sold') }}: </span>
                                <span class="value">{{ (int) $product->pivot->sold }} /
                                    {{ (int) $product->pivot->quantity }}</span>
                            @else
                                <span class="text text-danger">{{ __('Sold out') }}</span>
                            @endif
                        </span>
                    </div>
                @endif
                <div class="deal-progress col">
                    <div class="progress">
                        <div
                            class="progress-bar"
                            role="progressbar"
                            aria-label="{{ __('Sold out') }}"
                            aria-valuenow="{{ $product->pivot->quantity > 0 ? ($product->pivot->sold / $product->pivot->quantity) * 100 : 0 }}"
                            aria-valuemin="0"
                            aria-valuemax="100"
                            style="width: {{ $product->pivot->quantity > 0 ? ($product->pivot->sold / $product->pivot->quantity) * 100 : 0 }}%"
                        >
                        </div>
                    </div>
                </div>
            </div>
        @endisset
</div>
<div class="product-bottom-box">
    {!! Theme::partial('ecommerce.product-cart-form', compact('product')) !!}
</div>
</div>
