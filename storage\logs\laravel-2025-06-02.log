[2025-06-02 11:26:57] local.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: NO) (Connection: mysql, SQL: delete from `activations` where `completed` = 0 and `created_at` < 2025-05-30 11:26:56) - C:\Users\<USER>\Desktop\uu\vendor\laravel\framework\src\Illuminate\Database\Connection.php:829  
[2025-06-02 11:32:34] local.ERROR: Maximum execution time of 300 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 300 seconds exceeded at C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\ServeCommand.php:116)
[stacktrace]
#0 {main}
"} 
[2025-06-02 20:42:30] local.ERROR: Maximum execution time of 300 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 300 seconds exceeded at C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\ServeCommand.php:116)
[stacktrace]
#0 {main}
"} 
[2025-06-02 21:10:34] local.ERROR: Maximum execution time of 300 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 300 seconds exceeded at C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\ServeCommand.php:116)
[stacktrace]
#0 {main}
"} 
[2025-06-02 21:17:15] local.ERROR: Cannot access offset of type Illuminate\Database\Query\Expression on array (View: C:\Users\<USER>\Desktop\uu\platform\themes\farmart\views\ecommerce\product.blade.php) {"userId":1,"exception":"[object] (Illuminate\\View\\ViewException(code: 0): Cannot access offset of type Illuminate\\Database\\Query\\Expression on array (View: C:\\Users\\<USER>\\Desktop\\uu\\platform\\themes\\farmart\\views\\ecommerce\\product.blade.php) at C:\\Users\\<USER>\\Desktop\\uu\\platform\\themes\\farmart\\functions\\shortcodes.php:333)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(60): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(TypeError), 1)
#1 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#2 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#3 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#4 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Botble\\Shortcode\\View\\View->renderContents()
#5 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\theme\\src\\Theme.php(680): Illuminate\\View\\View->render()
#6 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\theme\\src\\Theme.php(659): Botble\\Theme\\Theme->setUpContent('theme.farmart::...', Array)
#7 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Botble\\Theme\\Theme->scope('ecommerce.produ...', Array, 'plugins/ecommer...')
#8 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\theme\\src\\Http\\Controllers\\PublicController.php(85): Illuminate\\Support\\Facades\\Facade::__callStatic('scope', Array)
#9 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\theme\\src\\Http\\Controllers\\PublicController.php(115): Botble\\Theme\\Http\\Controllers\\PublicController->getView('samsung-gear-vr...', 'products')
#10 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Botble\\Theme\\Http\\Controllers\\PublicController->getViewWithPrefix('products', 'samsung-gear-vr...')
#11 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getViewWithPref...', Array)
#12 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Botble\\Theme\\Http\\Controllers\\PublicController), 'getViewWithPref...')
#13 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():805}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\uu\\platform\\plugins\\language\\src\\Http\\Middleware\\LocalizationRedirectFilter.php(53): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\Language\\Http\\Middleware\\LocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\Desktop\\uu\\platform\\plugins\\language\\src\\Http\\Middleware\\LocaleSessionRedirect.php(81): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\Language\\Http\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Botble\\Base\\Http\\Middleware\\CoreMiddleware->{closure:Botble\\Base\\Http\\Middleware\\CoreMiddleware::handle():16}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\EnsureLicenseHasBeenActivated.php(24): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\Base\\Http\\Middleware\\EnsureLicenseHasBeenActivated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\uu\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\CaptureCouponMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\Ecommerce\\Http\\Middleware\\CaptureCouponMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\uu\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\CaptureFootprintsMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\Ecommerce\\Http\\Middleware\\CaptureFootprintsMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(29): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#56 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\platform\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#68 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#70 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#72 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#74 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#76 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#77 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#78 C:\\Users\\<USER>\\Desktop\\uu\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#79 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#80 {main}

[previous exception] [object] (TypeError(code: 0): Cannot access offset of type Illuminate\\Database\\Query\\Expression on array at C:\\Users\\<USER>\\Desktop\\uu\\platform\\themes\\farmart\\functions\\shortcodes.php:333)
[stacktrace]
#0 [internal function]: Illuminate\\Filesystem\\Filesystem::{closure:{closure:C:\\Users\\<USER>\\Desktop\\uu\\platform\\themes\\farmart\\functions\\shortcodes.php:23}:319}(Object(Botble\\Shortcode\\Compilers\\Shortcode), '', Object(Botble\\Shortcode\\Compilers\\ShortcodeCompiler), 'more-to-love')
#1 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\shortcode\\src\\Compilers\\ShortcodeCompiler.php(162): call_user_func_array(Object(Closure), Array)
#2 [internal function]: Botble\\Shortcode\\Compilers\\ShortcodeCompiler->render(Array)
#3 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\shortcode\\src\\Compilers\\ShortcodeCompiler.php(131): preg_replace_callback('/\\\\[(\\\\[?)(market...', Array, '[more-to-love][...')
#4 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\shortcode\\src\\Compilers\\ShortcodeCompiler.php(121): Botble\\Shortcode\\Compilers\\ShortcodeCompiler->renderShortcodes('[more-to-love][...')
#5 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\shortcode\\src\\Compilers\\ShortcodeCompiler.php(101): Botble\\Shortcode\\Compilers\\ShortcodeCompiler->parseToken(Array)
#6 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\shortcode\\src\\Shortcode.php(50): Botble\\Shortcode\\Compilers\\ShortcodeCompiler->compile('[more-to-love][...', true)
#7 C:\\Users\\<USER>\\Desktop\\uu\\platform\\packages\\shortcode\\helpers\\helpers.php(28): Botble\\Shortcode\\Shortcode->compile('[more-to-love][...', true)
#8 C:\\Users\\<USER>\\Desktop\\uu\\storage\\framework\\views\\3e054fecde082013fbebfdb3bdf85484.php(831): do_shortcode('[more-to-love][...')
#9 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#10 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():120}()
#11 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#12 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#13 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#14 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#15 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Botble\\Shortcode\\View\\View->renderContents()
#16 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\theme\\src\\Theme.php(680): Illuminate\\View\\View->render()
#17 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\theme\\src\\Theme.php(659): Botble\\Theme\\Theme->setUpContent('theme.farmart::...', Array)
#18 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Botble\\Theme\\Theme->scope('ecommerce.produ...', Array, 'plugins/ecommer...')
#19 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\theme\\src\\Http\\Controllers\\PublicController.php(85): Illuminate\\Support\\Facades\\Facade::__callStatic('scope', Array)
#20 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\theme\\src\\Http\\Controllers\\PublicController.php(115): Botble\\Theme\\Http\\Controllers\\PublicController->getView('samsung-gear-vr...', 'products')
#21 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Botble\\Theme\\Http\\Controllers\\PublicController->getViewWithPrefix('products', 'samsung-gear-vr...')
#22 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getViewWithPref...', Array)
#23 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Botble\\Theme\\Http\\Controllers\\PublicController), 'getViewWithPref...')
#24 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#25 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#26 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():805}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\uu\\platform\\plugins\\language\\src\\Http\\Middleware\\LocalizationRedirectFilter.php(53): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\Language\\Http\\Middleware\\LocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\uu\\platform\\plugins\\language\\src\\Http\\Middleware\\LocaleSessionRedirect.php(81): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\Language\\Http\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Botble\\Base\\Http\\Middleware\\CoreMiddleware->{closure:Botble\\Base\\Http\\Middleware\\CoreMiddleware::handle():16}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\EnsureLicenseHasBeenActivated.php(24): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\Base\\Http\\Middleware\\EnsureLicenseHasBeenActivated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\uu\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\CaptureCouponMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\Ecommerce\\Http\\Middleware\\CaptureCouponMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\uu\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\CaptureFootprintsMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\Ecommerce\\Http\\Middleware\\CaptureFootprintsMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(29): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#58 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#65 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#66 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#67 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#68 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#69 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#70 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\botble\\platform\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#71 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Botble\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#73 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#76 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#79 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#81 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#82 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#83 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#84 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#85 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#86 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#87 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#88 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#89 C:\\Users\\<USER>\\Desktop\\uu\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#90 C:\\Users\\<USER>\\Desktop\\uu\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#91 {main}
"} 
[2025-06-02 21:27:51] local.INFO: More to Love Debug {"total_products":65,"shown_products_count":30,"shown_product_ids":[64,19,63,45,62,49,30,15,59,25,13,29,58,26,46,36,61,31,12,40,7,3,60,11,17,44,56,18,32,55],"page":2} 
[2025-06-02 21:27:51] local.INFO: Products found after filtering {"available_products_count":0,"final_condition":{"ec_products.status":"published","ec_products.is_variation":0,"0":["ec_products.id","NOT IN",[64,19,63,45,62,49,30,15,59,25,13,29,58,26,46,36,61,31,12,40,7,3,60,11,17,44,56,18,32,55]]}} 
[2025-06-02 21:30:43] local.INFO: More to Love Debug {"total_products":65,"shown_products_count":30,"shown_product_ids":[17,55,56,62,9,58,16,18,8,20,46,25,45,60,63,42,52,28,29,54,43,36,12,21,35,5,26,50,37,24],"page":2} 
[2025-06-02 21:30:43] local.INFO: Products found after filtering {"available_products_count":0,"final_condition":{"ec_products.status":"published","ec_products.is_variation":0,"0":["ec_products.id","NOT IN",[17,55,56,62,9,58,16,18,8,20,46,25,45,60,63,42,52,28,29,54,43,36,12,21,35,5,26,50,37,24]]}} 
[2025-06-02 21:30:43] local.INFO: Forced cycle reset - no products available after filtering  
[2025-06-02 21:31:20] local.INFO: More to Love Debug {"total_products":65,"shown_products_count":30,"shown_product_ids":[35,19,9,40,50,46,56,63,33,30,44,3,48,12,47,64,39,38,60,32,8,53,52,65,31,42,36,11,41,7],"page":3} 
[2025-06-02 21:31:20] local.INFO: Products found after filtering {"available_products_count":0,"final_condition":{"ec_products.status":"published","ec_products.is_variation":0,"0":["ec_products.id","NOT IN",[35,19,9,40,50,46,56,63,33,30,44,3,48,12,47,64,39,38,60,32,8,53,52,65,31,42,36,11,41,7]]}} 
[2025-06-02 21:31:20] local.INFO: Forced cycle reset - no products available after filtering  
[2025-06-02 21:31:48] local.INFO: More to Love Debug {"total_products":65,"shown_products_count":30,"shown_product_ids":[24,13,5,1,64,16,62,3,56,20,42,40,41,25,52,55,51,33,31,22,21,35,12,23,6,26,8,59,57,19],"page":4} 
[2025-06-02 21:31:48] local.INFO: Products found after filtering {"available_products_count":0,"final_condition":{"ec_products.status":"published","ec_products.is_variation":0,"0":["ec_products.id","NOT IN",[24,13,5,1,64,16,62,3,56,20,42,40,41,25,52,55,51,33,31,22,21,35,12,23,6,26,8,59,57,19]]}} 
[2025-06-02 21:31:48] local.INFO: Forced cycle reset - no products available after filtering  
[2025-06-02 21:34:16] local.INFO: More to Love Debug {"total_products":65,"shown_products_count":30,"shown_product_ids":[6,43,17,22,55,7,48,5,51,64,30,37,19,2,8,9,61,47,62,54,15,4,33,63,53,27,10,57,50,52],"page":2} 
[2025-06-02 21:34:16] local.INFO: Products found after filtering {"available_products_count":0,"final_condition":{"ec_products.status":"published","ec_products.is_variation":0,"0":["ec_products.id","NOT IN",[6,43,17,22,55,7,48,5,51,64,30,37,19,2,8,9,61,47,62,54,15,4,33,63,53,27,10,57,50,52]]}} 
[2025-06-02 21:34:16] local.INFO: Forced cycle reset - no products available after filtering {"products_after_reset":65} 
[2025-06-02 21:34:20] local.INFO: More to Love Debug {"total_products":65,"shown_products_count":30,"shown_product_ids":[16,21,2,36,3,26,54,19,47,39,25,29,30,14,5,38,7,20,53,37,63,42,33,59,11,61,4,57,9,22],"page":3} 
[2025-06-02 21:34:20] local.INFO: Products found after filtering {"available_products_count":0,"final_condition":{"ec_products.status":"published","ec_products.is_variation":0,"0":["ec_products.id","NOT IN",[16,21,2,36,3,26,54,19,47,39,25,29,30,14,5,38,7,20,53,37,63,42,33,59,11,61,4,57,9,22]]}} 
[2025-06-02 21:34:20] local.INFO: Forced cycle reset - no products available after filtering {"products_after_reset":65} 
[2025-06-02 21:34:25] local.INFO: More to Love Debug {"total_products":65,"shown_products_count":30,"shown_product_ids":[25,45,60,39,19,57,1,54,50,55,20,12,15,22,24,36,11,52,10,65,56,63,4,47,23,30,62,40,8,21],"page":4} 
[2025-06-02 21:34:25] local.INFO: Products found after filtering {"available_products_count":0,"final_condition":{"ec_products.status":"published","ec_products.is_variation":0,"0":["ec_products.id","NOT IN",[25,45,60,39,19,57,1,54,50,55,20,12,15,22,24,36,11,52,10,65,56,63,4,47,23,30,62,40,8,21]]}} 
[2025-06-02 21:34:26] local.INFO: Forced cycle reset - no products available after filtering {"products_after_reset":65} 
[2025-06-02 21:37:27] local.INFO: More to Love Debug - Request Start {"total_products":65,"shown_products_count":30,"shown_product_ids":[47,23,36,52,58,2,55,51,37,61,1,38,30,22,9,16,63,21,56,32,44,59,5,25,28,42,11,7,62,39],"page":2,"raw_shown_ids_input":"47,23,36,52,58,2,55,51,37,61,1,38,30,22,9,16,63,21,56,32,44,59,5,25,28,42,11,7,62,39"} 
[2025-06-02 21:37:27] local.INFO: Products found after filtering {"available_products_count":0,"final_condition":{"ec_products.status":"published","ec_products.is_variation":0,"0":["ec_products.id","NOT IN",[47,23,36,52,58,2,55,51,37,61,1,38,30,22,9,16,63,21,56,32,44,59,5,25,28,42,11,7,62,39]]}} 
[2025-06-02 21:37:27] local.INFO: Forced cycle reset - no products available after filtering {"products_after_reset":65} 
[2025-06-02 21:37:28] local.INFO: More to Love Debug - Response {"new_product_ids":[22,50,15,7,11,34,19,9,28,27,51,13,6,10,14,59,55,30,17,16,1,43,24,37,39,48,45,54,60,62],"reset_cycle":true,"original_shown_ids":[],"final_condition_used":{"ec_products.status":"published","ec_products.is_variation":0,"0":["ec_products.id","NOT IN",[47,23,36,52,58,2,55,51,37,61,1,38,30,22,9,16,63,21,56,32,44,59,5,25,28,42,11,7,62,39]]},"products_found_count":30} 
[2025-06-02 21:37:34] local.INFO: More to Love Debug - Request Start {"total_products":65,"shown_products_count":30,"shown_product_ids":[22,50,15,7,11,34,19,9,28,27,51,13,6,10,14,59,55,30,17,16,1,43,24,37,39,48,45,54,60,62],"page":3,"raw_shown_ids_input":"22,50,15,7,11,34,19,9,28,27,51,13,6,10,14,59,55,30,17,16,1,43,24,37,39,48,45,54,60,62"} 
[2025-06-02 21:37:34] local.INFO: Products found after filtering {"available_products_count":0,"final_condition":{"ec_products.status":"published","ec_products.is_variation":0,"0":["ec_products.id","NOT IN",[22,50,15,7,11,34,19,9,28,27,51,13,6,10,14,59,55,30,17,16,1,43,24,37,39,48,45,54,60,62]]}} 
[2025-06-02 21:37:34] local.INFO: Forced cycle reset - no products available after filtering {"products_after_reset":65} 
[2025-06-02 21:37:35] local.INFO: More to Love Debug - Response {"new_product_ids":[44,9,37,5,40,11,12,60,20,31,28,26,48,19,50,51,41,64,7,49,32,54,17,56,63,65,24,38,61,34],"reset_cycle":true,"original_shown_ids":[],"final_condition_used":{"ec_products.status":"published","ec_products.is_variation":0,"0":["ec_products.id","NOT IN",[22,50,15,7,11,34,19,9,28,27,51,13,6,10,14,59,55,30,17,16,1,43,24,37,39,48,45,54,60,62]]},"products_found_count":30} 
[2025-06-02 21:37:40] local.INFO: More to Love Debug - Request Start {"total_products":65,"shown_products_count":30,"shown_product_ids":[44,9,37,5,40,11,12,60,20,31,28,26,48,19,50,51,41,64,7,49,32,54,17,56,63,65,24,38,61,34],"page":4,"raw_shown_ids_input":"44,9,37,5,40,11,12,60,20,31,28,26,48,19,50,51,41,64,7,49,32,54,17,56,63,65,24,38,61,34"} 
[2025-06-02 21:37:40] local.INFO: Products found after filtering {"available_products_count":0,"final_condition":{"ec_products.status":"published","ec_products.is_variation":0,"0":["ec_products.id","NOT IN",[44,9,37,5,40,11,12,60,20,31,28,26,48,19,50,51,41,64,7,49,32,54,17,56,63,65,24,38,61,34]]}} 
[2025-06-02 21:37:40] local.INFO: Forced cycle reset - no products available after filtering {"products_after_reset":65} 
[2025-06-02 21:37:41] local.INFO: More to Love Debug - Response {"new_product_ids":[46,26,15,44,56,31,64,53,3,61,22,19,8,57,13,37,17,59,29,33,11,41,10,38,14,62,24,39,34,51],"reset_cycle":true,"original_shown_ids":[],"final_condition_used":{"ec_products.status":"published","ec_products.is_variation":0,"0":["ec_products.id","NOT IN",[44,9,37,5,40,11,12,60,20,31,28,26,48,19,50,51,41,64,7,49,32,54,17,56,63,65,24,38,61,34]]},"products_found_count":30} 
